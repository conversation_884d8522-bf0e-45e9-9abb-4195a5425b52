/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.plan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.Condition;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.finance.enums.financeApply.FinanceApplyEnums;
import org.springblade.plan.dto.CreateOrderDTO;
import org.springblade.plan.dto.TradingOrderDataQueryDTO;
import org.springblade.plan.entity.TradingOrderData;
import org.springblade.plan.enums.OrderTypeEnum;
import org.springblade.plan.service.ITradingOrderDataService;
import org.springblade.plan.vo.TradingOrderDataVO;
import org.springblade.plan.wrapper.TradingOrderDataWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 交易订单数据表前端控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-plan/web-front/tradingOrderData")
@Api(value = "交易订单数据表前端", tags = "交易订单数据表前端接口")
public class TradingOrderDataFrontController extends BladeController {

    private final ITradingOrderDataService tradingOrderDataService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入id")
    public R<TradingOrderDataVO> detail(@RequestParam Long id) {
        TradingOrderData detail = tradingOrderDataService.getById(id);
        return R.data(TradingOrderDataWrapper.build().entityVO(detail));
    }

    /**
     * 我的订单分页查询
     */
    @GetMapping("/myOrders")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "我的订单分页", notes = "查询当前用户的订单")
    public R<IPage<TradingOrderData>> myOrders(TradingOrderDataQueryDTO tradingOrderData, Query query) {
        // 设置当前用户ID
        tradingOrderData.setUserId(AuthUtil.getUserId());
        IPage<TradingOrderData> pages = tradingOrderDataService.page(Condition.getPage(query),
                Condition.getQueryWrapper(tradingOrderData, TradingOrderData.class).lambda().orderByDesc(TradingOrderData::getCreateTime)
        );
        return R.data(pages);
    }
    /**
     * 我的订单分页查询
     */
    @GetMapping("/myFinanceUseOrders")
    @ApiOperation(value = "我的订单分页", notes = "查询当前用户的订单")
    public R<IPage<TradingOrderData>> myFinanceUseOrders(TradingOrderDataQueryDTO tradingOrderData, Query query) {
        // 设置当前用户ID
        tradingOrderData.setUserId(AuthUtil.getUserId());
        return R.data(tradingOrderDataService.myFinanceUseOrders(tradingOrderData));
    }

    /**
     * 生成订单数据
     */
    @PostMapping("/createOrder")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "生成订单数据", notes = "传入订单金额和场景类型")
    public R<TradingOrderDataVO> createOrder(@RequestParam BigDecimal orderAmount,
                                           @RequestParam Integer scenarioType) {
        Long userId = AuthUtil.getUserId();
        TradingOrderData orderData = tradingOrderDataService.createOrder(userId, orderAmount, scenarioType);
        return R.data(TradingOrderDataWrapper.build().entityVO(orderData));
    }

    /**
     * 生成订单数据（完整版）
     */
    @PostMapping("/createOrderFull")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "生成订单数据（完整版）", notes = "传入订单金额、场景类型、业务类型和订单类型")
    public R<TradingOrderDataVO> createOrderFull(@RequestParam BigDecimal orderAmount,
                                               @RequestParam Integer scenarioType,
                                               @RequestParam Integer orderType) {
        Long userId = AuthUtil.getUserId();
        TradingOrderData orderData = tradingOrderDataService.createOrder(userId, orderAmount, scenarioType);
        // 设置业务类型和订单类型
        orderData.setOrderType(orderType);
        tradingOrderDataService.updateById(orderData);
        return R.data(TradingOrderDataWrapper.build().entityVO(orderData));
    }

    /**
     * 创建订单（使用DTO）
     */
    @PostMapping("/createOrderWithDTO")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "创建订单（使用DTO）", notes = "使用DTO创建订单，支持完整参数")
    public R<TradingOrderDataVO> createOrderWithDTO(@Valid @RequestBody CreateOrderDTO createOrderDTO) {
        Long userId = AuthUtil.getUserId();
        TradingOrderData orderData = tradingOrderDataService.createOrder(userId,
                createOrderDTO.getOrderAmount(), createOrderDTO.getScenarioType());

        // 设置扩展字段
        orderData.setOrderType(createOrderDTO.getOrderType());

        // 如果提供了卖方和买方信息，则更新
        if (createOrderDTO.getSalesCreditCode() != null) {
            orderData.setSalesCreditCode(createOrderDTO.getSalesCreditCode());
        }
        if (createOrderDTO.getSalesCompanyName() != null) {
            orderData.setSalesCompanyName(createOrderDTO.getSalesCompanyName());
        }
        if (createOrderDTO.getBuyerCreditCode() != null) {
            orderData.setBuyerCreditCode(createOrderDTO.getBuyerCreditCode());
        }

        // 更新订单数据
        tradingOrderDataService.updateById(orderData);
        return R.data(TradingOrderDataWrapper.build().entityVO(orderData));
    }

    /**
     * 根据订单编号查询
     */
    @GetMapping("/getByOrderNo")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "根据订单编号查询", notes = "传入订单编号")
    public R<TradingOrderDataVO> getByOrderNo(@RequestParam String orderNo) {
        TradingOrderData orderData = tradingOrderDataService.lambdaQuery()
                .eq(TradingOrderData::getOrderNo, orderNo)
                .eq(TradingOrderData::getUserId, AuthUtil.getUserId())
                .one();
        return R.data(TradingOrderDataWrapper.build().entityVO(orderData));
    }

    /**
     * 统计我的订单数量
     */
    @GetMapping("/countMyOrders")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "统计我的订单数量", notes = "统计当前用户的订单数量")
    public R<Long> countMyOrders() {
        long count = tradingOrderDataService.lambdaQuery()
                .eq(TradingOrderData::getUserId, AuthUtil.getUserId())
                .count();
        return R.data(count);
    }

    /**
     * 获取业务类型选项
     */
    @GetMapping("/businessTypeOptions")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "获取业务类型选项", notes = "获取所有业务类型选项")
    public R<Map<Integer, String>> getBusinessTypeOptions() {
        Map<Integer, String> options = new HashMap<>();
        // 只返回代采融资和订单融资两种类型
        options.put(FinanceApplyEnums.GoodsTypeEnum.AGENT_TYPE.getCode(), FinanceApplyEnums.GoodsTypeEnum.AGENT_TYPE.getDesc());
        options.put(FinanceApplyEnums.GoodsTypeEnum.ORDER_FINANCING_TYPE.getCode(), FinanceApplyEnums.GoodsTypeEnum.ORDER_FINANCING_TYPE.getDesc());
        return R.data(options);
    }

    /**
     * 获取订单类型选项
     */
    @GetMapping("/orderTypeOptions")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "获取订单类型选项", notes = "获取所有订单类型选项")
    public R<Map<Integer, String>> getOrderTypeOptions() {
        Map<Integer, String> options = new HashMap<>();
        for (OrderTypeEnum typeEnum : OrderTypeEnum.values()) {
            options.put(typeEnum.getCode(), typeEnum.getDesc());
        }
        return R.data(options);
    }


}
