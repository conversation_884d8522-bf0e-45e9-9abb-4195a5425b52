package org.springblade.plan.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantContextHolder;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.customer.entity.CustomerInfo;
import org.springblade.customer.entity.CustomerSupplier;
import org.springblade.customer.service.ICustomerInfoService;
import org.springblade.customer.service.ICustomerSupplierService;
import org.springblade.plan.dto.CreateOrderDTO;
import org.springblade.plan.dto.TradingOrderDataQueryDTO;
import org.springblade.plan.entity.TradingOrderData;
import org.springblade.plan.enums.OrderTypeEnum;
import org.springblade.plan.enums.SystemTypeEnum;
import org.springblade.plan.enums.TradingOrderScenarioTypeEnum;
import org.springblade.plan.enums.TradingOrderStatusEnum;
import org.springblade.plan.mapper.TradingOrderDataMapper;
import org.springblade.plan.publisher.CreateOrderSuccessPublisher;
import org.springblade.plan.service.ITradingOrderDataService;
import org.springblade.plan.vo.TradingOrderDataVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * 测试订单数据表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TradingOrderDataServiceImpl extends BaseServiceImpl<TradingOrderDataMapper, TradingOrderData> implements ITradingOrderDataService {
    private final ICustomerInfoService customerInfoService;
    private final CreateOrderSuccessPublisher createOrderSuccessPublisher;
    private final ICustomerSupplierService customerSupplierService;

    /**
     * 生成订单数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradingOrderData createOrder(Long userId, BigDecimal orderAmount, Integer scenarioType) {
        CustomerInfo customerInfo = customerInfoService.getByCompanyId(userId);
        if (ObjectUtil.isEmpty(customerInfo)) {
            throw new ServiceException("融资用户不存在");
        }
        String tenantId = Func.toStr(TenantContextHolder.getTenantId(), Func.toStr(AuthUtil.getTenantId(), "000000"));
        //构建订单
        TradingOrderData orderData = this.buildOrder(userId, customerInfo, orderAmount, scenarioType);

        if (TradingOrderScenarioTypeEnum.ONE_TO_MANY.getCode().equals(scenarioType)) {
            createOrderSuccessPublisher.pushCreateOrderSuccess(Collections.singletonList(orderData), scenarioType, tenantId);
        } else {
            List<TradingOrderData> orderList = this.list(Wrappers.<TradingOrderData>lambdaQuery()
                    .eq(TradingOrderData::getUserId, userId)
                    .eq(TradingOrderData::getScenarioType, TradingOrderScenarioTypeEnum.MANY_TO_ONE.getCode())
                    .eq(TradingOrderData::getStatus, TradingOrderStatusEnum.ORDER_UN_USE.getCode())
            );
            orderList.add(orderData);
            createOrderSuccessPublisher.pushCreateOrderSuccess(orderList, scenarioType, tenantId);
        }
        this.save(orderData);
        return orderData;
    }

    /**
     * 自定义分页查询
     */
    @Override
    public IPage<TradingOrderDataVO> selectTradingOrderDataPage(IPage<TradingOrderData> page, TradingOrderDataVO tradingOrderData) {
        List<TradingOrderDataVO> records = baseMapper.selectTradingOrderDataPage(page, tradingOrderData);
        Page<TradingOrderDataVO> pageVO = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageVO.setRecords(records);
        pageVO.setPages(page.getPages());
        return pageVO;
    }

    private TradingOrderData buildOrder(Long userId, CustomerInfo customerInfo, BigDecimal orderAmount, Integer scenarioType) {
        TradingOrderData orderData = new TradingOrderData();
        orderData.setUserId(userId);
        orderData.setCompanyId(customerInfo.getCompanyId());
        orderData.setCompanyName(customerInfo.getCorpName());
        orderData.setBuyerCreditCode(customerInfo.getBusinessLicenceNumber());
        orderData.setSalesCompanyName("默认卖家");
        orderData.setSalesCreditCode("12345678987654321");
        orderData.setGoodsName("默认商品");
        orderData.setGoodsPrice(orderAmount);
        orderData.setDepositRatio(BigDecimal.ZERO);
        orderData.setUnit("默认单位");
        orderData.setQuantity(1);
        orderData.setSpec("默认规格");
        orderData.setDeliverTime(LocalDate.now().plusDays(30));
        orderData.setOrderAmount(orderAmount);
        orderData.setOrderNo(CodeUtil.generateCode(CodeEnum.PROOF_NO));
        orderData.setOrderCreateTime(LocalDate.now().toString());
        orderData.setScenarioType(scenarioType);
        orderData.setStatus(TradingOrderStatusEnum.ORDER_UN_USE.getCode());
        // 设置默认订单类型为其他
        orderData.setOrderType(OrderTypeEnum.DEFAULT.getCode());
        return orderData;
    }

    /**
     * 生成订单数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TradingOrderData createOrder(CreateOrderDTO orderDTO) {
        // 校验图片URL是否有效
        if (!validateImageUrl(orderDTO.getGoodsLogo())) {
            throw new ServiceException("商品图片无效或无法访问，请重新上传");
        }
        //融资用户信息
        CustomerInfo customerInfo = customerInfoService.getByCompanyId(orderDTO.getCompanyId());
        if (ObjectUtil.isEmpty(customerInfo)) {
            throw new ServiceException("融资用户不存在");
        }

        //供应商信息
        CustomerSupplier customerSupplier = customerSupplierService.getById(orderDTO.getSupplierId());
        if (ObjectUtil.isEmpty(customerSupplier)) {
            throw new ServiceException("供应商不存在");
        }

        String tenantId = Func.toStr(TenantContextHolder.getTenantId(), Func.toStr(AuthUtil.getTenantId(), "000000"));

        //构建订单
        TradingOrderData orderData = this.buildOrder(orderDTO, customerInfo, customerSupplier);
//        if(true){
//            throw new ServiceException("tessss");
//        }
        Integer scenarioType = orderData.getScenarioType();
        if (TradingOrderScenarioTypeEnum.ONE_TO_MANY.getCode().equals(scenarioType)) {
            createOrderSuccessPublisher.pushCreateOrderSuccess(Collections.singletonList(orderData), scenarioType, tenantId);
        } else {
            List<TradingOrderData> orderList = this.list(Wrappers.<TradingOrderData>lambdaQuery()
                    .eq(TradingOrderData::getUserId, orderData.getCompanyId())
                    .eq(TradingOrderData::getScenarioType, TradingOrderScenarioTypeEnum.MANY_TO_ONE.getCode())
                    .eq(TradingOrderData::getStatus, TradingOrderStatusEnum.ORDER_UN_USE.getCode())
            );
            orderList.add(orderData);
            createOrderSuccessPublisher.pushCreateOrderSuccess(orderList, scenarioType, tenantId);
        }
        this.save(orderData);
        return orderData;
    }

    private TradingOrderData buildOrder(TradingOrderData order, CustomerInfo customerInfo, CustomerSupplier customerSupplier) {
        TradingOrderData orderData = BeanUtil.copyProperties(order, TradingOrderData.class);
        orderData.setUserId(customerInfo.getCompanyId());
        orderData.setCompanyName(customerInfo.getCorpName());
        orderData.setBuyerCreditCode(customerInfo.getBusinessLicenceNumber());
        orderData.setSalesCompanyName(customerSupplier.getSupperName());
        orderData.setSalesCreditCode(customerSupplier.getUnifiedCode());
        orderData.setDepositRatio(BigDecimal.ZERO);
        orderData.setDeliverTime(LocalDate.now().plusDays(30));
        orderData.setOrderNo(CodeUtil.generateCode(CodeEnum.PROOF_NO));
        orderData.setOrderCreateTime(LocalDate.now().toString());
        orderData.setStatus(TradingOrderStatusEnum.ORDER_UN_USE.getCode());
        orderData.setSource(SystemTypeEnum.GONG_JIN.getCode());
        // 设置默认订单类型为其他
        orderData.setOrderType(null == orderData.getOrderType() ? OrderTypeEnum.DEFAULT.getCode() : orderData.getOrderType());
        return orderData;
    }

    /**
     * 校验图片URL是否有效
     * @param imageUrl 图片URL
     * @return true-有效，false-无效
     */
    private boolean validateImageUrl(String imageUrl) {
        if (ObjectUtil.isEmpty(imageUrl)) {
            throw new ServiceException("图片URL不能为空");
        }

        try {
            // 检查URL格式
            if (!imageUrl.startsWith("http://") && !imageUrl.startsWith("https://")) {
                log.warn("图片URL格式不正确: {}", imageUrl);
                return false;
            }

            // 发送HEAD请求检查图片是否可访问
            int statusCode = HttpUtil.createGet(imageUrl)
                    //5秒超时
                    .timeout(5000)
                    .execute()
                    .getStatus();

            if (statusCode == 200) {
                log.info("图片URL校验成功: {}", imageUrl);
                return true;
            } else {
                log.warn("图片URL访问失败，状态码: {}, URL: {}", statusCode, imageUrl);
                return false;
            }
        } catch (Exception e) {
            log.error("图片URL校验异常: {}, URL: {}", e.getMessage(), imageUrl);
            return false;
        }
    }

    /**
     * 获取可融资使用的订单
     * @param tradingOrderData
     * @return
     */
    @Override
    public IPage<TradingOrderData> myFinanceUseOrders(TradingOrderDataQueryDTO tradingOrderData) {
        return null;
    }
}
